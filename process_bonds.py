import pandas as pd
import numpy as np
from scipy.stats import norm
from scipy.integrate import quad

# Constants (to be confirmed by user)
CALL_COEF = 1.0  # Call price coefficient
RF_RATE = 0.03   # Risk-free rate (3%)
PROTECT_YEARS = 2  # Protection period

# 1. Consolidate data
def consolidate_excel(file_path):
    xls = pd.ExcelFile(file_path)
    sheet_names = xls.sheet_names
    
    # Create mapping of bond codes to names
    first_sheet = pd.read_excel(xls, sheet_name=sheet_names[0], nrows=1)
    bond_codes = first_sheet.columns[1:]
    bond_names = first_sheet.iloc[0, 1:].values
    code_to_name = dict(zip(bond_codes, bond_names))
    
    # Initialize final DataFrame
    consolidated = pd.DataFrame()
    
    for sheet in sheet_names:
        # Read sheet and handle date column properly
        df = pd.read_excel(xls, sheet_name=sheet)
        
        # The first row contains bond names, so skip it
        df = df.iloc[1:]
        
        # Rename first column to '交易日期'
        df = df.rename(columns={df.columns[0]: '交易日期'})
        
        # Melt to long format
        df_melted = df.melt(
            id_vars='交易日期', 
            var_name='证券代码', 
            value_name=sheet
        )
        
        # Merge sheets
        if consolidated.empty:
            consolidated = df_melted
        else:
            consolidated = consolidated.merge(
                df_melted, 
                on=['交易日期', '证券代码'], 
                how='outer'
            )
    
    # Add bond names and filter pre-listing periods
    consolidated['证券简称'] = consolidated['证券代码'].map(code_to_name)
    consolidated = consolidated.dropna(subset=sheet_names, how='all')
    
    return consolidated

# 2. CCB model functions (from user's specification)
def _d_pm(S0, K, rf, sigma, tau, sign=+1):
    num = np.log(S0 / K) + (rf + 0.5 * sigma**2) * tau
    denom = sigma * np.sqrt(tau)
    return num / denom - (sign < 0) * denom

def _first_hit_pdf(t, S0, C, mu, sigma):
    ln_ratio = np.log(C / S0)
    return ln_ratio * np.exp(-(ln_ratio - mu*t)**2 / (2*sigma**2*t)) / \
           (sigma * np.sqrt(2*np.pi*t**3))

def equity_path_A1(S0, C, rf, sigma, tp):
    if tp <= 0: return 0.0
    d1 = _d_pm(S0, C, rf, sigma, tp, +1)
    return S0 * norm.cdf(d1)

from functools import lru_cache

@lru_cache(maxsize=128)
def cached_first_hit_pdf(t, S0, C, mu, sigma):
    return _first_hit_pdf(t, S0, C, mu, sigma)

def equity_path_A2(S0, C, rf, sigma, tp, T):
    if T <= tp: return 0.0
    mu_val = rf - 0.5 * sigma**2
    f = lambda t: np.exp(-rf*t) * cached_first_hit_pdf(t, S0, C, mu_val, sigma)
    val, _ = quad(f, tp, T, epsrel=1e-3, limit=50)
    return C * val

def equity_path_A3(S0, C, N_adj, rf, sigma, tp, T):
    tau = T - tp
    if tau <= 0: return 0.0
    lam = (rf + 0.5 * sigma**2) / sigma**2
    d1 = _d_pm(S0, N_adj, rf, sigma, tau, +1)
    e1 = (np.log(C**2 / (S0*N_adj)) + (rf + 0.5*sigma**2)*tau) / (sigma * np.sqrt(tau))
    return S0 * (norm.cdf(d1) - (C/S0)**(2*lam) * norm.cdf(e1))

def ccb_equity_value(row):
    try:
        S0 = row['转换价值']
        sigma = row['隐含波动率']
        if np.isnan(sigma): 
            return np.nan
        
        T = row['剩余期限']
        C = CALL_COEF * row['转股价']
        N_adj = row['纯债价值']
        tp = max(0.0, PROTECT_YEARS - max(0, row['上市距今年']))
        
        # Calculate components with error handling
        a1 = equity_path_A1(S0, C, RF_RATE, sigma, tp)
        a2 = equity_path_A2(S0, C, RF_RATE, sigma, tp, T)
        a3 = equity_path_A3(S0, C, N_adj, RF_RATE, sigma, tp, T)
        return a1 + a2 + a3
    except Exception as e:
        print(f"Error calculating CCB equity for bond {row['证券代码']} on {row['交易日期']}: {str(e)}")
        return np.nan

# 3. Calculate bond classification
def calculate_classification(row):
    # Calculate premiums
    conversion_premium = (row['债券均价'] - row['转换价值']) / row['转换价值']
    pure_bond_premium = (row['债券均价'] - row['纯债价值']) / row['纯债价值']
    
    # Classify bond type (rules to be confirmed)
    if conversion_premium <= 0.05:
        return '偏股型', conversion_premium, pure_bond_premium
    elif conversion_premium <= 0.30:
        return '平衡型', conversion_premium, pure_bond_premium
    else:
        return '偏债型', conversion_premium, pure_bond_premium

# Main processing
def process_data(input_path, output_path):
    # Step 1: Consolidate data
    df = consolidate_excel(input_path)
    
    # Step 2: Prepare for CCB calculation
    df = df.sort_values(['证券代码', '交易日期'])
    df['上市距今年'] = df.groupby('证券代码').cumcount() / 252
    
    # Create output Excel writer
    writer = pd.ExcelWriter(output_path, engine='openpyxl')
    
    # Create empty DataFrame for header
    final_cols = [
        '证券代码', '证券简称', '交易日期', 
        '债券开盘价', '债券均价', '正股收盘价', '正股均价', 
        '剩余期限', '纯债价值', '转股价', '转换价值', '隐含波动率',
        'B_equity', 'CCB_theo', '定价偏离因子',
        '转股溢价率', '纯债溢价率', '债券类型'
    ]
    pd.DataFrame(columns=final_cols).to_excel(writer, index=False)
    
    # Process each bond individually and write immediately
    bond_codes = df['证券代码'].unique()
    for i, bond_code in enumerate(bond_codes):
        print(f"Processing bond {i+1}/{len(bond_codes)}: {bond_code}")
        bond_df = df[df['证券代码'] == bond_code].copy()
        
        # Calculate only essential columns
        bond_df['B_equity'] = bond_df.apply(ccb_equity_value, axis=1)
        bond_df['CCB_theo'] = bond_df['B_equity'] + bond_df['纯债价值']
        bond_df['定价偏离因子'] = (bond_df['债券均价'] / bond_df['CCB_theo']) - 1
        
        # Calculate bond classification without creating intermediate columns
        classifications = bond_df.apply(
            lambda row: calculate_classification(row), 
            axis=1
        )
        bond_df['债券类型'] = classifications.apply(lambda x: x[0])
        bond_df['转股溢价率'] = classifications.apply(lambda x: x[1])
        bond_df['纯债溢价率'] = classifications.apply(lambda x: x[2])
        
        # Append to Excel file
        bond_df[final_cols].to_excel(
            writer, 
            startrow=writer.sheets['Sheet1'].max_row,
            index=False, 
            header=False
        )
        
        # Clear memory
        del bond_df
    
    # Save and close Excel writer
    writer.close()
    print(f"Processing complete. Output saved to {output_path}")
    return None

if __name__ == "__main__":
    input_file = "/Users/<USER>/Desktop/转债量化.xlsx"
    output_file = "/Users/<USER>/Desktop/转债量化/consolidated_bonds.xlsx"
    process_data(input_file, output_file)
    print(f"Processing complete. Output saved to {output_file}")
