# ==============================================================================
# 10年国债期货（T）周度宏观领先指数合成代码 (优化版)
#
# 作者: Gemini (Based on user's logic and optimization)
# 日期: 2025-07-10
#
# 更新日志:
# - 假定输入数据已为周度频率。
# - 移除对电影票房的特殊缺失值处理。
# - 采用通用填充方法处理少量缺失值。
# - 将所有结果输出到指定桌面文件夹。
# ==============================================================================

# --- 第一步：环境准备与数据加载 ---

# 导入必要的Python库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os # 导入os库以处理文件路径
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib以支持中文显示（确保您的系统已安装中文字体，如SimHei）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- 定义输入与输出路径 ---
# 输入文件路径
file_path = '/Users/<USER>/Desktop/国债期货量化因子.xlsx'
sheet_name = '周频因子'

# 输出文件夹路径（请确保该文件夹已创建）
output_dir = '/Users/<USER>/Desktop/国债期货量化结果/'

# 创建输出文件夹（如果不存在）
os.makedirs(output_dir, exist_ok=True)

# 使用try-except结构以处理可能的文件不存在错误
try:
    # 加载数据，不设置索引列
    df = pd.read_excel(file_path, sheet_name=sheet_name)
    print('原始数据加载成功！')
    print('原始数据预览：')
    print(df.head())
    print('数据形状：', df.shape)
    print('列名：', list(df.columns))

    # 检查第一列是否包含日期数据
    first_col = df.columns[0]
    print(f'\n第一列名称：{first_col}')
    print('第一列前10个值：')
    print(df[first_col].head(10))

    # 尝试将第一列转换为日期时间格式，跳过无法解析的行
    valid_dates = []
    valid_indices = []

    for i, value in enumerate(df[first_col]):
        try:
            # 尝试转换为日期时间
            date_val = pd.to_datetime(value)
            valid_dates.append(date_val)
            valid_indices.append(i)
        except:
            # 如果无法转换，跳过这一行
            print(f'跳过第{i+1}行，无法解析的值：{value}')
            continue

    # 只保留有效日期的行
    df = df.iloc[valid_indices].copy()
    df.index = valid_dates
    df = df.drop(columns=[first_col])  # 删除原来的日期列

    print(f'\n清理后数据形状：{df.shape}')
    print('清理后数据预览：')
    print(df.head())

except FileNotFoundError:
    print(f'错误：无法在路径 \'{file_path}\' 找到文件。请检查文件路径和名称是否正确。')
    exit()
except Exception as e:
    print(f'加载数据时出错：{e}')
    exit()

# --- 第二步：缺失值处理 ---

print('\n开始处理缺失值...')
print('处理前缺失值统计：')
print(df.isnull().sum())

# 对所有列的少量、零星缺失值进行前向填充，再用后向填充处理开头可能存在的缺失
df = df.fillna(method='ffill').fillna(method='bfill')
print('所有缺失值已通过前向/后向填充处理完毕。')
print('\n缺失值处理后，再次检查是否还有缺失：')
# isnull().sum().sum() 计算整个DataFrame中NaN的总数
if df.isnull().sum().sum() == 0:
    print('数据完整，无缺失值。')
else:
    print('警告：数据中仍存在缺失值，请检查原始数据。')
    print(df.isnull().sum())

# --- 第三步：因子计算与方向统一化 ---

print('\n开始进行因子计算与方向统一化处理...')

# 创建一个新的DataFrame来存储处理后的因子，以保持原始数据不变
processed_factors = df.copy()

# 3.1 计算衍生的复合因子
try:
    # 检查所需列是否存在（使用实际的列名）
    required_cols = ['中债企业债到期收益率(AA):10年:周:平均值', '中债国债到期收益率:10年:周:平均值', '市盈率:沪深300指数:周:平均值',
                    '加权平均价(主力合约):沪铜(9:00-15:00):周:平均值', '加权平均价(主力合约):沪金(9:00-15:00):周:平均值']

    missing_cols = [col for col in required_cols if col not in processed_factors.columns]
    if missing_cols:
        print(f'警告：以下列不存在，将跳过相关计算：{missing_cols}')

    # 信用利差: (AA企业债 - 10Y国债)，然后取负
    if '中债企业债到期收益率(AA):10年:周:平均值' in processed_factors.columns and '中债国债到期收益率:10年:周:平均值' in processed_factors.columns:
        processed_factors['信用利差'] = -(processed_factors['中债企业债到期收益率(AA):10年:周:平均值'] - processed_factors['中债国债到期收益率:10年:周:平均值'])
        print('信用利差计算完成')

    # 股债ERP: (1/沪深300市盈率 - 10Y国债收益率)，然后取负
    if '市盈率:沪深300指数:周:平均值' in processed_factors.columns and '中债国债到期收益率:10年:周:平均值' in processed_factors.columns:
        processed_factors['股债ERP'] = -(1 / processed_factors['市盈率:沪深300指数:周:平均值'] - processed_factors['中债国债到期收益率:10年:周:平均值'] / 100)
        print('股债ERP计算完成')

    # 铜金比: (沪铜/沪金)，然后取负
    if '加权平均价(主力合约):沪铜(9:00-15:00):周:平均值' in processed_factors.columns and '加权平均价(主力合约):沪金(9:00-15:00):周:平均值' in processed_factors.columns:
        processed_factors['铜金比'] = -(processed_factors['加权平均价(主力合约):沪铜(9:00-15:00):周:平均值'] / processed_factors['加权平均价(主力合约):沪金(9:00-15:00):周:平均值'])
        print('铜金比计算完成')

except Exception as e:
    print(f'计算衍生因子时出错：{e}')

# 3.2 定义并批量反向处理原始因子（统一为数值越高，对T合约越【利好】）
factors_to_invert = [
    '中国:高炉开工率(247家)', '中国出口集装箱运价指数:综合指数', '北京:地铁客运量:周:平均值',
    '中国:票房收入:电影:周:平均值', '中国:30大中城市:成交面积:商品房:周:平均值', 'R007:周:平均值',
    'DR007:周:平均值', 'R007-DR007', '南华工业品指数:周:平均值', '唐山:价格:螺纹钢(HRB400,20mm):周:平均值',
    '波动率:50ETF期权:周:平均值', '恒生AH股溢价指数:周:平均值'
]

inverted_count = 0
for col in factors_to_invert:
    if col in processed_factors.columns:
        processed_factors[col] = -processed_factors[col]
        inverted_count += 1
    else:
        print(f'警告：在反向处理列表中找不到因子 \'{col}\'，已跳过。')
print(f'已反向处理 {inverted_count} 个因子，所有因子方向已统一完毕。')

# --- 第四步：因子标准化 ---

print('\n开始对所有因子进行滚动Z-Score标准化...')

# 设置滚动窗口期为52周（一年）
rolling_window = 52

# 定义所有需要进行标准化的最终因子列表
final_factor_columns = []
for col in factors_to_invert:
    if col in processed_factors.columns:
        final_factor_columns.append(col)

# 添加衍生因子
derived_factors = ['信用利差', '股债ERP', '铜金比']
for col in derived_factors:
    if col in processed_factors.columns:
        final_factor_columns.append(col)

print(f'将对以下 {len(final_factor_columns)} 个因子进行标准化：{final_factor_columns}')

# 创建一个新的DataFrame来存储标准化后的Z-Score值
standardized_factors = pd.DataFrame(index=processed_factors.index)

for col in final_factor_columns:
    rolling_mean = processed_factors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).mean()
    rolling_std = processed_factors[col].rolling(window=rolling_window, min_periods=int(rolling_window/2)).std()
    # 为避免除以0，在分母上加一个极小值
    standardized_factors[col + '_Z'] = (processed_factors[col] - rolling_mean) / (rolling_std + 1e-10)

# 再次填充由于滚动计算在开头产生的NaN
standardized_factors = standardized_factors.fillna(method='bfill')
print(f'滚动 {rolling_window} 周的Z-Score标准化完成。')

# --- 第五步：分项指数与最终指数合成 ---

print('\n开始合成三大分项指数和最终领先指数...')

# 定义各分项指数的构成因子（使用标准化后的列名后缀 "_Z"）
econ_inflation_factors = [
    '中国:高炉开工率(247家)_Z', '中国出口集装箱运价指数:综合指数_Z', '北京:地铁客运量:周:平均值_Z',
    '中国:票房收入:电影:周:平均值_Z', '中国:30大中城市:成交面积:商品房:周:平均值_Z', '南华工业品指数:周:平均值_Z',
    '唐山:价格:螺纹钢(HRB400,20mm):周:平均值_Z'
]
econ_inflation_factors = [f for f in econ_inflation_factors if f in standardized_factors.columns]

liquidity_factors = [
    'R007:周:平均值_Z', 'DR007:周:平均值_Z', 'R007-DR007_Z'
]
liquidity_factors = [f for f in liquidity_factors if f in standardized_factors.columns]

risk_appetite_factors = [
    '信用利差_Z', '股债ERP_Z', '铜金比_Z', '波动率:50ETF期权:周:平均值_Z', '恒生AH股溢价指数:周:平均值_Z'
]
risk_appetite_factors = [f for f in risk_appetite_factors if f in standardized_factors.columns]

print(f'实体与通胀指数因子：{econ_inflation_factors}')
print(f'金融流动性指数因子：{liquidity_factors}')
print(f'市场风险偏好指数因子：{risk_appetite_factors}')

# 创建一个DataFrame用于存放指数结果
index_results = pd.DataFrame(index=df.index)

# 使用等权重法计算各分项指数
if econ_inflation_factors:
    index_results['实体与通胀指数'] = standardized_factors[econ_inflation_factors].mean(axis=1)
else:
    index_results['实体与通胀指数'] = 0
    print('警告：实体与通胀指数无可用因子')

if liquidity_factors:
    index_results['金融流动性指数'] = standardized_factors[liquidity_factors].mean(axis=1)
else:
    index_results['金融流动性指数'] = 0
    print('警告：金融流动性指数无可用因子')

if risk_appetite_factors:
    index_results['市场风险偏好指数'] = standardized_factors[risk_appetite_factors].mean(axis=1)
else:
    index_results['市场风险偏好指数'] = 0
    print('警告：市场风险偏好指数无可用因子')

print('三大分项指数已通过等权重法合成。')

# 使用专家权重法合成最终的领先指数
weights = {'金融流动性指数': 0.5, '市场风险偏好指数': 0.3, '实体与通胀指数': 0.2}
index_results['最终领先指数'] = (index_results['金融流动性指数'] * weights['金融流动性指数'] +
                               index_results['市场风险偏好指数'] * weights['市场风险偏好指数'] +
                               index_results['实体与通胀指数'] * weights['实体与通胀指数'])
print('最终领先指数已通过专家权重法合成。')

# --- 第六步：结果存储与可视化 ---

print('\n开始存储结果并进行可视化...')

# 6.1 将所有结果合并到一个DataFrame中
# 合并原始数据、清洗后数据、标准化因子、各项指数
final_results = pd.concat([df, processed_factors[final_factor_columns], standardized_factors, index_results], axis=1)

# 6.2 存储到指定的输出文件夹
output_excel_path = os.path.join(output_dir, '国债期货量化指数结果.xlsx')
output_plot_path = os.path.join(output_dir, '国债期货领先指数可视化图表.png')

try:
    final_results.to_excel(output_excel_path)
    print(f'所有结果已成功保存至：{output_excel_path}')
except Exception as e:
    print(f'错误：保存Excel文件失败。原因：{e}')

# 6.3 可视化最终领先指数和三个分项指数
plt.figure(figsize=(20, 12))

# 绘制最终领先指数
ax1 = plt.subplot(2, 1, 1)
ax1.plot(index_results.index, index_results['最终领先指数'], label='最终领先指数', color='red', linewidth=2)
ax1.axhline(1.5, color='gray', linestyle='--', linewidth=1, label='强看多阈值 (+1.5)')
ax1.axhline(-1.5, color='gray', linestyle='-.', linewidth=1, label='强风险警示 (-1.5)')
# 填充阈值区域
ax1.fill_between(index_results.index, 1.5, index_results['最终领先指数'], where=(index_results['最终领先指数'] >= 1.5), color='mistyrose', interpolate=True)
ax1.fill_between(index_results.index, -1.5, index_results['最终领先指数'], where=(index_results['最终领先指数'] <= -1.5), color='lightcyan', interpolate=True)
ax1.set_title('国债期货周度宏观领先指数', fontsize=16)
ax1.legend()
ax1.grid(True, linestyle='--', alpha=0.6)
ax1.set_ylabel('指数值 (Z-Score化)')

# 绘制三个分项指数
ax2 = plt.subplot(2, 1, 2, sharex=ax1) # 共享X轴
ax2.plot(index_results.index, index_results['实体与通胀指数'], label='实体与通胀指数', color='blue', alpha=0.8)
ax2.plot(index_results.index, index_results['金融流动性指数'], label='金融流动性指数', color='green', alpha=0.8)
ax2.plot(index_results.index, index_results['市场风险偏好指数'], label='市场风险偏好指数', color='purple', alpha=0.8)
ax2.set_title('三大分项指数走势', fontsize=16)
ax2.legend()
ax2.grid(True, linestyle='--', alpha=0.6)
ax2.set_ylabel('指数值 (等权重合成)')
ax2.set_xlabel('日期')

plt.tight_layout()

# 保存图表到指定文件夹
try:
    plt.savefig(output_plot_path, dpi=300, bbox_inches='tight')
    print(f'可视化图表已成功保存至：{output_plot_path}')
except Exception as e:
    print(f'错误：保存图表失败。原因：{e}')

# 显示最终结果统计
print('\n=== 最终结果统计 ===')
print('最终领先指数统计：')
print(index_results['最终领先指数'].describe())
print('\n各分项指数相关性：')
print(index_results[['实体与通胀指数', '金融流动性指数', '市场风险偏好指数']].corr())

print('\n代码执行完毕。')
