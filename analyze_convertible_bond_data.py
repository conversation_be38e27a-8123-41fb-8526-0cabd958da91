#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债量化分析 - 数据文件结构分析
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path

def analyze_excel_structure():
    """分析Excel文件结构"""
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    
    print(f"分析文件: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path) / 1024 / 1024:.2f} MB")
    
    try:
        # 读取所有sheet名称
        xl = pd.ExcelFile(file_path)
        print(f"\n总共有 {len(xl.sheet_names)} 个sheet:")
        
        sheet_info = {}
        for i, sheet_name in enumerate(xl.sheet_names):
            print(f"{i+1:2d}. {sheet_name}")
            
            # 读取每个sheet的基本信息
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=10)
                sheet_info[sheet_name] = {
                    'shape': df.shape,
                    'columns': list(df.columns),
                    'sample_data': df.head(3)
                }
            except Exception as e:
                print(f"    读取失败: {e}")
                sheet_info[sheet_name] = {'error': str(e)}
        
        # 详细分析每个sheet
        print("\n" + "="*80)
        print("详细Sheet分析:")
        print("="*80)
        
        for sheet_name, info in sheet_info.items():
            print(f"\n【{sheet_name}】")
            if 'error' in info:
                print(f"  错误: {info['error']}")
                continue
                
            print(f"  数据维度: {info['shape']}")
            print(f"  列名: {info['columns']}")
            print("  样本数据:")
            print(info['sample_data'].to_string(index=False))
            print("-" * 60)
        
        return xl, sheet_info
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None, None

def clean_and_merge_data():
    """清洗并合并所有sheet的数据"""
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')

    print("开始数据清洗与合并...")

    # 读取所有sheet
    xl = pd.ExcelFile(file_path)

    # 定义sheet名称映射
    sheet_mapping = {
        '债券收盘价': 'bond_close',
        '债券均价': 'bond_avg',
        '正股收盘价': 'stock_close',
        '正股均价': 'stock_avg',
        '剩余期限': 'remaining_term',
        '转股价': 'conversion_price',
        '隐含波动率': 'implied_volatility',
        '纯债价值': 'bond_value',
        '转换价值': 'conversion_value'
    }

    # 存储所有数据
    all_data = {}

    for sheet_name in xl.sheet_names:
        if sheet_name in sheet_mapping:
            print(f"处理 {sheet_name}...")
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # 检查第一列的内容
            print(f"  第一列名称: {df.columns[0]}")
            print(f"  前几行数据: {df.iloc[:3, 0].tolist()}")

            # 设置第一列为时间索引，跳过标题行
            if df.iloc[0, 0] == '时间':
                df = df.iloc[1:].reset_index(drop=True)  # 跳过第一行

            df.set_index(df.columns[0], inplace=True)
            df.index = pd.to_datetime(df.index)
            df.index.name = 'date'

            all_data[sheet_mapping[sheet_name]] = df

    # 获取所有转债代码
    bond_codes = all_data['bond_close'].columns.tolist()

    print(f"总共有 {len(bond_codes)} 只转债")
    print(f"时间范围: {all_data['bond_close'].index.min()} 到 {all_data['bond_close'].index.max()}")

    # 合并数据
    merged_data = []

    for date in all_data['bond_close'].index:
        for bond_code in bond_codes:
            # 检查该日期该转债是否有数据（至少有一个指标不为空）
            has_data = False
            row_data = {'证券代码': bond_code, '交易日期': date}

            for indicator, df in all_data.items():
                value = df.loc[date, bond_code] if bond_code in df.columns else np.nan
                if pd.notna(value):
                    has_data = True
                row_data[indicator] = value

            # 只有当该转债在该日期有数据时才添加
            if has_data:
                merged_data.append(row_data)

    # 转换为DataFrame
    result_df = pd.DataFrame(merged_data)

    # 重新排列列的顺序
    column_order = ['证券代码', '交易日期', 'bond_close', 'bond_avg', 'stock_close', 'stock_avg',
                   'remaining_term', 'bond_value', 'conversion_price', 'conversion_value', 'implied_volatility']

    # 重命名列
    column_names = {
        'bond_close': '债券收盘价',
        'bond_avg': '债券均价',
        'stock_close': '正股收盘价',
        'stock_avg': '正股均价',
        'remaining_term': '剩余期限',
        'bond_value': '纯债价值',
        'conversion_price': '转股价',
        'conversion_value': '转换价值',
        'implied_volatility': '隐含波动率'
    }

    result_df = result_df[column_order].rename(columns=column_names)

    print(f"合并后数据形状: {result_df.shape}")
    print(f"数据样本:")
    print(result_df.head())

    return result_df

if __name__ == "__main__":
    # xl, sheet_info = analyze_excel_structure()
    clean_df = clean_and_merge_data()
