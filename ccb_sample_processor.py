#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债CCB模型高精度定价与因子计算 - 样本处理版
严格按照学术文献要求实现
"""

import pandas as pd
import numpy as np
import os
from scipy.stats import norm, multivariate_normal
import warnings
warnings.filterwarnings('ignore')

# CCB模型参数设置（按照任务要求）
RF_RATE = 0.025         # 无风险利率 2.5%
CALL_COEF = 1.3         # 赎回触发系数 130%

# =======================================================================================
# Block 3: CCB模型股权价值计算工具 (高精度复现版，基于文献附录公式)
# =======================================================================================

def ccb_equity_value_precise(S0, K1_fv_N, h, rf, sigma, tp, T):
    """
    基于文献附录公式高精度计算CCB股权价值。
    
    参数:
    S0 (float): 当前转股价值 (平价)
    K1_fv_N (float): 经信用风险调整后的到期本息终值 (对应文献中的 fv_N)
    h (float): 赎回触发价 (对应文献中的 h)
    rf (float): 无风险利率
    sigma (float): 年化波动率
    tp (float): 剩余保护期 (年)
    T (float): 剩余到期时间 (年)
    
    返回:
    float: CCB模型的股权理论价值
    """
    # ----------------- 1. 初始化中间变量 (源于文献附录一) -----------------
    if T <= 0 or sigma <= 0:
        return 0.0
        
    u = rf - 0.5 * sigma**2
    u_hat = rf + 0.5 * sigma**2
    u_tilde = np.sqrt(u**2 + 2 * sigma**2 * rf)
    
    K1 = np.log(K1_fv_N / S0)
    K2 = np.log(h / S0)

    # ----------------- 2. 定义二元正态分布CDF辅助函数 N2(a, b, rho) -----------------
    def N2(a, b, rho):
        if np.isinf(a) or np.isinf(b): 
            return norm.cdf(min(a,b)) # 简化处理无穷大情况
        mean = [0, 0]
        cov = [[1, rho], [rho, 1]]
        return multivariate_normal.cdf([a, b], mean=mean, cov=cov)

    # ----------------- 3. 计算路径A1的价值 (保护期末直接高于赎回线) -----------------
    if tp > 0:
        d_hat_1 = (np.log(S0 / h) + u_hat * tp) / (sigma * np.sqrt(tp))
        val_A1 = S0 * norm.cdf(d_hat_1)
    else:
        val_A1 = max(0, S0 - h)

    # ----------------- 4. 计算路径A2的价值 (保护期后、到期前首次触及) -----------------
    if T > tp > 0:
        # G(tau)函数
        def G(tau):
            if tau <= 0:
                return 0
            term1 = N2(-np.inf, ((-K2 + u_tilde * tau) / (sigma * np.sqrt(tau))), 0)
            term2 = np.exp(2 * u_tilde * K2 / sigma**2) * N2(-np.inf, ((-K2 - u_tilde * tau) / (sigma * np.sqrt(tau))), 0)
            return term1 + term2
        
        # H(tau1, tau2)函数
        def H(t1, t2):
            if t1 <= 0 or t2 <= 0:
                return 0
            term1 = N2((-K2 + u_tilde * t1) / (sigma * np.sqrt(t1)), 
                      (K2 - u_tilde * t2) / (sigma * np.sqrt(t2)), 
                      -np.sqrt(t1/t2))
            term2 = np.exp(2 * u_tilde * K2 / sigma**2) * \
                   N2((-K2 - u_tilde * t1) / (sigma * np.sqrt(t1)), 
                      (K2 + u_tilde * t2) / (sigma * np.sqrt(t2)), 
                      -np.sqrt(t1/t2))
            return term1 + term2
                           
        val_A2_prob = H(tp, T) + (G(T) - G(tp))
        val_A2 = h * val_A2_prob
    else:
        val_A2 = 0.0

    # ----------------- 5. 计算路径A3的价值 (到期日高于纯债价值) -----------------
    if T > 0:
        if tp > 0:
            term1_A3 = N2((K2 - u_hat * tp) / (sigma * np.sqrt(tp)), 
                         (K2 - u_hat * T) / (sigma * np.sqrt(T)), 
                         np.sqrt(tp/T))
            term2_A3 = np.exp(2 * u_hat * K2 / sigma**2) * \
                      N2((K2 + u_hat * tp) / (sigma * np.sqrt(tp)), 
                         (-K2 - u_hat * T) / (sigma * np.sqrt(T)), 
                         -np.sqrt(tp/T))
            val_A3_prob_S_gt_h = term1_A3 - term2_A3

            term1_A3_k1 = N2((K2 - u_hat * tp) / (sigma * np.sqrt(tp)), 
                             (K1 - u_hat * T) / (sigma * np.sqrt(T)), 
                             np.sqrt(tp/T))
            term2_A3_k1 = np.exp(2 * u_hat * K2 / sigma**2) * \
                         N2((K2 + u_hat * tp) / (sigma * np.sqrt(tp)), 
                            (K1 - 2*K2 - u_hat * T) / (sigma * np.sqrt(T)), 
                            -np.sqrt(tp/T))
            val_A3_prob_S_gt_k1 = term1_A3_k1 - term2_A3_k1
        else:
            # 当tp=0时的简化处理
            d1 = (K1 - u_hat * T) / (sigma * np.sqrt(T))
            d2 = (K2 - u_hat * T) / (sigma * np.sqrt(T))
            val_A3_prob_S_gt_k1 = norm.cdf(d1)
            val_A3_prob_S_gt_h = norm.cdf(d2)
        
        val_A3 = S0 * np.exp(rf * T) * max(0, val_A3_prob_S_gt_k1 - val_A3_prob_S_gt_h)
    else:
        val_A3 = 0.0

    return val_A1 + val_A2 + val_A3

def load_sample_data():
    """
    加载样本数据（前50个交易日）进行测试
    """
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')
    
    print("开始加载样本数据...")
    print(f"读取文件: {file_path}")
    
    # 定义需要读取的Sheet列表
    sheet_list = [
        '债券收盘价', '债券均价', '正股收盘价', '正股均价', 
        '剩余期限', '纯债价值', '转股价', '转换价值', '隐含波动率'
    ]
    
    # 读取第一个sheet获取基本信息
    df_base = pd.read_excel(file_path, sheet_name='债券收盘价', nrows=52)  # 标题行+50个交易日
    df_base = df_base.iloc[1:51].reset_index(drop=True)  # 跳过标题行，取前50行
    
    # 获取时间序列和转债代码
    dates = pd.to_datetime(df_base.iloc[:, 0])
    bond_codes = df_base.columns[1:51].tolist()  # 取前50只转债
    
    print(f"样本时间范围: {dates.min()} 到 {dates.max()}")
    print(f"样本转债数量: {len(bond_codes)}")
    
    # 存储结果
    result_data = []
    
    # 逐个sheet处理
    for sheet_name in sheet_list:
        print(f"处理Sheet: {sheet_name}")
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=52)
            df = df.iloc[1:51, :51].reset_index(drop=True)  # 前50行，前50列
            
            # 转换为长格式
            for i, date in enumerate(dates):
                for j, bond_code in enumerate(bond_codes):
                    if j < len(df.columns) - 1:  # 确保不超出列范围
                        value = df.iloc[i, j+1]  # j+1因为第一列是时间
                        if pd.notna(value):
                            # 查找或创建记录
                            record_key = (date, bond_code)
                            existing_record = None
                            for record in result_data:
                                if record['交易日期'] == date and record['证券代码'] == bond_code:
                                    existing_record = record
                                    break
                            
                            if existing_record is None:
                                new_record = {
                                    '证券代码': bond_code,
                                    '证券简称': bond_code.split('.')[0],
                                    '交易日期': date
                                }
                                for indicator in sheet_list:
                                    new_record[indicator] = np.nan
                                new_record[sheet_name] = value
                                result_data.append(new_record)
                            else:
                                existing_record[sheet_name] = value
        except Exception as e:
            print(f"警告：处理Sheet '{sheet_name}' 时出错: {e}")
            continue
    
    # 转换为DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 清洗：只保留至少有5个指标不为空的记录
    indicator_cols = sheet_list
    result_df['non_null_count'] = result_df[indicator_cols].count(axis=1)
    result_df = result_df[result_df['non_null_count'] >= 5].drop('non_null_count', axis=1)
    
    # 排序
    result_df = result_df.sort_values(['交易日期', '证券代码']).reset_index(drop=True)
    
    print(f"样本数据形状: {result_df.shape}")
    
    return result_df

def calculate_ccb_pricing_sample(df):
    """
    计算CCB定价（样本版）
    """
    print("开始计算CCB理论价值...")
    
    df = df.copy()
    
    def compute_ccb_equity(row):
        try:
            S0 = row['转换价值']
            sigma = row['隐含波动率']
            T = row['剩余期限']
            conversion_price = row['转股价']
            bond_value = row['纯债价值']
            
            if pd.isna(S0) or pd.isna(sigma) or pd.isna(T) or pd.isna(conversion_price) or pd.isna(bond_value):
                return np.nan
                
            if S0 <= 0 or sigma <= 0 or T <= 0 or conversion_price <= 0 or bond_value <= 0:
                return np.nan
            
            h = CALL_COEF * conversion_price
            K1_fv_N = bond_value
            tp = max(0, min(2.0, T))
            
            equity_value = ccb_equity_value_precise(
                S0=S0, K1_fv_N=K1_fv_N, h=h, rf=RF_RATE, 
                sigma=sigma, tp=tp, T=T
            )
            
            return equity_value
            
        except Exception as e:
            return np.nan
    
    df['CCB股权价值'] = df.apply(compute_ccb_equity, axis=1)
    df['CCB理论价值'] = df['CCB股权价值'] + df['纯债价值']
    df['CCB定价偏离因子'] = (df['债券收盘价'] / df['CCB理论价值']) - 1
    
    # 其他因子
    df['转股溢价率'] = (df['债券收盘价'] - df['转换价值']) / df['转换价值']
    df['纯债溢价率'] = (df['债券收盘价'] - df['纯债价值']) / df['纯债价值']
    df['转股价值占比'] = df['转换价值'] / df['债券收盘价']
    df['纯债价值占比'] = df['纯债价值'] / df['债券收盘价']

    # 转债分类
    def classify_bond(row):
        conversion_premium = row['转股溢价率']
        bond_premium = row['纯债溢价率']
        conversion_ratio = row['转股价值占比']

        if pd.isna(conversion_premium) or pd.isna(conversion_ratio):
            return '未分类'

        # 偏股型：转股溢价率较低，转股价值占比较高
        if conversion_premium < 0.2 and conversion_ratio > 0.7:
            return '偏股型'
        # 偏债型：转股溢价率较高，转股价值占比较低
        elif conversion_premium > 0.5 and conversion_ratio < 0.5:
            return '偏债型'
        # 平衡型：介于两者之间
        else:
            return '平衡型'

    df['转债类型'] = df.apply(classify_bond, axis=1)

    return df

def main():
    """
    主函数
    """
    print("可转债CCB模型高精度定价与因子计算 - 样本版")
    print("="*60)
    
    # 加载样本数据
    df = load_sample_data()
    
    # 计算CCB定价
    df = calculate_ccb_pricing_sample(df)
    
    # 详细分析结果
    print(f"\n" + "="*80)
    print("CCB模型定价分析结果")
    print("="*80)
    print(f"总记录数: {len(df):,}")
    print(f"转债数量: {df['证券代码'].nunique()}")
    print(f"时间范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")
    print(f"CCB模型成功计算比例: {df['CCB理论价值'].notna().mean()*100:.1f}%")

    # 转债类型分布
    print(f"\n转债类型分布:")
    type_dist = df['转债类型'].value_counts()
    for bond_type, count in type_dist.items():
        pct = count / len(df) * 100
        print(f"  {bond_type}: {count:,} ({pct:.1f}%)")

    # 关键指标统计
    if df['CCB理论价值'].notna().sum() > 0:
        valid_df = df[df['CCB理论价值'].notna()]

        print(f"\n关键指标统计（基于{len(valid_df)}个有效样本）:")

        metrics = {
            'CCB定价偏离因子': valid_df['CCB定价偏离因子'],
            '转股溢价率': valid_df['转股溢价率'],
            '纯债溢价率': valid_df['纯债溢价率'],
            '转股价值占比': valid_df['转股价值占比']
        }

        for metric_name, metric_data in metrics.items():
            if metric_data.notna().sum() > 0:
                clean_data = metric_data.dropna()
                print(f"  {metric_name}:")
                print(f"    均值: {clean_data.mean():.4f}")
                print(f"    标准差: {clean_data.std():.4f}")
                print(f"    中位数: {clean_data.median():.4f}")
                print(f"    最小值: {clean_data.min():.4f}")
                print(f"    最大值: {clean_data.max():.4f}")

        # CCB模型理论分析
        print(f"\nCCB模型理论价值分析:")
        ccb_theory = valid_df['CCB理论价值']
        bond_price = valid_df['债券收盘价']
        print(f"  CCB理论价值均值: {ccb_theory.mean():.2f}")
        print(f"  实际债券价格均值: {bond_price.mean():.2f}")
        print(f"  理论价值/实际价格比值: {(ccb_theory.mean()/bond_price.mean()):.4f}")

        # 定价偏离分析
        deviation = valid_df['CCB定价偏离因子']
        print(f"\n定价偏离深度分析:")
        print(f"  负偏离（低估）比例: {(deviation < 0).mean()*100:.1f}%")
        print(f"  正偏离（高估）比例: {(deviation > 0).mean()*100:.1f}%")
        print(f"  偏离绝对值均值: {deviation.abs().mean():.4f}")

        # 按转债类型分析
        print(f"\n按转债类型的定价偏离分析:")
        for bond_type in valid_df['转债类型'].unique():
            if pd.notna(bond_type):
                type_data = valid_df[valid_df['转债类型'] == bond_type]['CCB定价偏离因子']
                if len(type_data) > 0:
                    print(f"  {bond_type}: 均值={type_data.mean():.4f}, 样本数={len(type_data)}")
    
    # 保存结果
    output_dir = os.path.expanduser('~/Desktop/转债量化')
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, 'CCB模型定价样本结果.xlsx')
    df.to_excel(output_file, index=False)
    print(f"\n样本结果已保存到: {output_file}")
    
    # 显示样本
    display_cols = ['证券代码', '证券简称', '交易日期', '债券收盘价', 'CCB理论价值', 'CCB定价偏离因子']
    available_cols = [col for col in display_cols if col in df.columns]
    print(f"\n样本数据预览:")
    print(df[available_cols].head(10).to_string(index=False))
    
    return df

if __name__ == "__main__":
    result_df = main()
