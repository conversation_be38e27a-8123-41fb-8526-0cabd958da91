#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债量化分析 - 高效数据处理与CCB模型实现
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path
from scipy.stats import norm
from scipy.integrate import quad
import warnings
warnings.filterwarnings('ignore')

# CCB模型参数设置
RF_RATE = 0.03          # 无风险利率 3%
CALL_COEF = 1.3         # 赎回系数 130%
PROTECT_YEARS = 2.0     # 保护期 2年

def efficient_data_merge():
    """高效的数据合并方法"""
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')
    
    print("开始高效数据处理...")
    
    # 定义sheet名称映射
    sheet_mapping = {
        '债券收盘价': '债券收盘价',
        '债券均价': '债券均价', 
        '正股收盘价': '正股收盘价',
        '正股均价': '正股均价',
        '剩余期限': '剩余期限',
        '转股价': '转股价',
        '隐含波动率': '隐含波动率',
        '纯债价值': '纯债价值',
        '转换价值': '转换价值'
    }
    
    # 读取所有数据
    all_dfs = []
    
    for sheet_name, col_name in sheet_mapping.items():
        print(f"处理 {sheet_name}...")
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 跳过第一行标题，设置时间索引
        df = df.iloc[1:].reset_index(drop=True)
        df.set_index(df.columns[0], inplace=True)
        df.index = pd.to_datetime(df.index)
        df.index.name = 'date'
        
        # 转换为长格式
        df_long = df.stack().reset_index()
        df_long.columns = ['交易日期', '证券代码', col_name]
        
        # 去除空值
        df_long = df_long.dropna(subset=[col_name])
        
        all_dfs.append(df_long)
    
    # 合并所有数据
    print("合并数据...")
    result_df = all_dfs[0]  # 从第一个开始
    
    for df in all_dfs[1:]:
        result_df = result_df.merge(df, on=['交易日期', '证券代码'], how='outer')
    
    # 只保留至少有一个指标不为空的记录
    result_df = result_df.dropna(how='all', subset=list(sheet_mapping.values()))
    
    # 添加证券简称（从代码中提取）
    result_df['证券简称'] = result_df['证券代码'].apply(lambda x: x.split('.')[0])
    
    # 重新排列列顺序
    cols = ['证券代码', '证券简称', '交易日期'] + list(sheet_mapping.values())
    result_df = result_df[cols]
    
    # 排序
    result_df = result_df.sort_values(['交易日期', '证券代码']).reset_index(drop=True)
    
    print(f"合并后数据形状: {result_df.shape}")
    print(f"时间范围: {result_df['交易日期'].min()} 到 {result_df['交易日期'].max()}")
    print(f"转债数量: {result_df['证券代码'].nunique()}")
    
    return result_df

# CCB模型实现
def _d_pm(S0, K, rf, sigma, tau, sign=+1):
    """计算d+/-参数"""
    if tau <= 0 or sigma <= 0:
        return 0.0
    num = np.log(S0 / K) + (rf + 0.5 * sigma**2) * tau
    denom = sigma * np.sqrt(tau)
    return num / denom - (sign < 0) * denom

def _first_hit_pdf(t, S0, C, mu, sigma):
    """首次触及概率密度函数"""
    if t <= 0 or sigma <= 0:
        return 0.0
    ln_ratio = np.log(C / S0)
    return ln_ratio * np.exp(-(ln_ratio - mu*t)**2 / (2*sigma**2*t)) / \
           (sigma * np.sqrt(2*np.pi*t**3))

def equity_path_A1(S0, C, rf, sigma, tp):
    """路径A1：保护期内不触及赎回价格"""
    if tp <= 0 or sigma <= 0:
        return 0.0
    d1 = _d_pm(S0, C, rf, sigma, tp, +1)
    return S0 * norm.cdf(d1)

def equity_path_A2(S0, C, rf, sigma, tp, T):
    """路径A2：保护期后触及赎回价格"""
    if T <= tp or sigma <= 0:
        return 0.0
    mu = rf - 0.5 * sigma**2
    
    def integrand(t):
        return np.exp(-rf*t) * _first_hit_pdf(t, S0, C, mu, sigma)
    
    try:
        val, _ = quad(integrand, tp, T, epsrel=1e-6, limit=100)
        return C * val
    except:
        return 0.0

def equity_path_A3(S0, C, N_adj, rf, sigma, tp, T):
    """路径A3：到期时未触及赎回价格"""
    tau = T - tp
    if tau <= 0 or sigma <= 0:
        return 0.0
    
    lam = (rf + 0.5 * sigma**2) / sigma**2
    d1 = _d_pm(S0, N_adj, rf, sigma, tau, +1)
    
    if S0 <= 0 or N_adj <= 0:
        return 0.0
        
    e1 = (np.log(C**2 / (S0*N_adj)) + (rf + 0.5*sigma**2)*tau) / \
         (sigma * np.sqrt(tau))
    
    return S0 * (norm.cdf(d1) - (C/S0)**(2*lam) * norm.cdf(e1))

def ccb_equity_value(row):
    """计算CCB模型股权价值"""
    try:
        S0 = row['转换价值']
        sigma = row['隐含波动率']
        T = row['剩余期限']
        conversion_price = row['转股价']
        bond_value = row['纯债价值']
        
        # 检查必要数据
        if pd.isna(S0) or pd.isna(sigma) or pd.isna(T) or pd.isna(conversion_price) or pd.isna(bond_value):
            return np.nan
            
        if S0 <= 0 or sigma <= 0 or T <= 0 or conversion_price <= 0 or bond_value <= 0:
            return np.nan
        
        # 计算参数
        C = CALL_COEF * conversion_price  # 赎回价格
        N_adj = bond_value  # 调整后的债券价值
        
        # 计算上市距今年数（简化处理，假设从数据开始日期算起）
        years_since_listing = max(0, row.get('上市距今年', 0))
        tp = max(0.0, PROTECT_YEARS - years_since_listing)
        
        # 计算三个路径的价值
        a1 = equity_path_A1(S0, C, RF_RATE, sigma, tp)
        a2 = equity_path_A2(S0, C, RF_RATE, sigma, tp, T)
        a3 = equity_path_A3(S0, C, N_adj, RF_RATE, sigma, tp, T)
        
        return a1 + a2 + a3
        
    except Exception as e:
        return np.nan

def add_ccb_pricing(df):
    """添加CCB定价相关指标"""
    print("计算CCB股权价值...")
    
    # 添加上市距今年数（简化处理）
    df = df.copy()
    df['上市距今年'] = df.groupby('证券代码').cumcount() / 252  # 假设252个交易日为一年
    
    # 计算CCB股权价值
    df['CCB股权价值'] = df.apply(ccb_equity_value, axis=1)
    
    # 计算CCB理论价值
    df['CCB理论价值'] = df['CCB股权价值'] + df['纯债价值']
    
    # 计算定价偏离因子
    df['CCB定价偏离因子'] = (df['债券收盘价'] / df['CCB理论价值']) - 1
    
    return df

def calculate_additional_factors(df):
    """计算转股溢价率、纯债溢价率等因子"""
    print("计算附加因子...")
    
    df = df.copy()
    
    # 转股溢价率 = (转债价格 - 转换价值) / 转换价值
    df['转股溢价率'] = (df['债券收盘价'] - df['转换价值']) / df['转换价值']
    
    # 纯债溢价率 = (转债价格 - 纯债价值) / 纯债价值  
    df['纯债溢价率'] = (df['债券收盘价'] - df['纯债价值']) / df['纯债价值']
    
    # 转股价值占比 = 转换价值 / 转债价格
    df['转股价值占比'] = df['转换价值'] / df['债券收盘价']
    
    # 纯债价值占比 = 纯债价值 / 转债价格
    df['纯债价值占比'] = df['纯债价值'] / df['债券收盘价']
    
    return df

def classify_convertible_bonds(df):
    """根据溢价率等因子对转债进行分类"""
    print("转债分类...")
    
    df = df.copy()
    
    # 分类规则（可以根据实际情况调整）
    def classify_bond(row):
        conversion_premium = row['转股溢价率']
        bond_premium = row['纯债溢价率']
        conversion_ratio = row['转股价值占比']
        
        if pd.isna(conversion_premium) or pd.isna(bond_premium):
            return '未分类'
        
        # 偏股型：转股溢价率较低，转股价值占比较高
        if conversion_premium < 0.2 and conversion_ratio > 0.7:
            return '偏股型'
        # 偏债型：纯债溢价率较低，纯债价值占比较高
        elif bond_premium < 0.3 and conversion_ratio < 0.5:
            return '偏债型'
        # 平衡型：介于两者之间
        else:
            return '平衡型'
    
    df['转债类型'] = df.apply(classify_bond, axis=1)
    
    return df

if __name__ == "__main__":
    # 执行数据处理流程
    clean_df = efficient_data_merge()
    
    # 添加CCB定价
    clean_df = add_ccb_pricing(clean_df)
    
    # 计算附加因子
    clean_df = calculate_additional_factors(clean_df)
    
    # 转债分类
    clean_df = classify_convertible_bonds(clean_df)
    
    print(f"\n最终数据形状: {clean_df.shape}")
    print(f"列名: {list(clean_df.columns)}")
    print(f"\n数据样本:")
    print(clean_df.head())
    
    # 保存结果
    output_dir = os.path.expanduser('~/Desktop/转债量化')
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, '转债量化分析结果.xlsx')
    clean_df.to_excel(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
