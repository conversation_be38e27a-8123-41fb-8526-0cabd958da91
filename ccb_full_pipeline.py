#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债CCB模型高精度定价与全流程因子计算
严格按照学术文献要求实现，包含动态无风险利率处理
"""

import pandas as pd
import numpy as np
import os
from scipy.stats import norm, multivariate_normal
from scipy.integrate import quad
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings('ignore')

# CCB模型参数设置（按照任务要求）
CALL_COEF = 1.3                    # 赎回触发系数 130%
TOTAL_PROTECTION_YEARS = 0.5       # 总保护期 6个月

# =======================================================================================
# Block 3: CCB模型股权价值计算工具 (最终优化版，基于文献附录金融逻辑)
# =======================================================================================

def ccb_equity_value_precise(S0, K1_fv_N, h, rf, sigma, tp, T):
    """
    基于文献附录的金融逻辑，高精度计算CCB股权价值。
    该函数将转债的股权价值拆解为三种互斥路径(A1, A2, A3)并求和。

    参数:
    S0 (float): 当前转股价值 (平价)
    K1_fv_N (float): 经信用风险调整后的到期本息终值 (对应文献中的 fv_N)
    h (float): 赎回触发价 (对应文献中的 h)
    rf (float): 动态获取的、与期限T匹配的无风险利率
    sigma (float): 年化波动率
    tp (float): 动态计算的、最终的剩余保护期 (年)
    T (float): 剩余到期时间 (年)
    
    返回:
    float: CCB模型的股权理论价值
    """
    # ----------------- 0. 输入校验与边缘情况处理 -----------------
    if pd.isna(S0) or pd.isna(K1_fv_N) or pd.isna(h) or pd.isna(rf) or pd.isna(sigma) or pd.isna(tp) or pd.isna(T):
        return np.nan
    if S0 <= 0 or sigma <= 0 or T <= 0:
        return 0.0
    if T <= tp: # 如果剩余期限小于保护期，则简化为无赎回的欧式期权
        d1 = (np.log(S0 / K1_fv_N) + (rf + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        return S0 * norm.cdf(d1) - K1_fv_N * np.exp(-rf * T) * norm.cdf(d2)

    # ----------------- 1. 初始化模型中间变量 -----------------
    u = rf - 0.5 * sigma**2
    u_hat = rf + 0.5 * sigma**2
    
    # K1是对数到期行权价, K2是对数赎回障碍价
    K1 = np.log(K1_fv_N / S0)
    K2 = np.log(h / S0)

    # ----------------- 2. 路径A1: 保护期末(tp)直接高于赎回价(h)的价值 -----------------
    # 金融含义：这是一个资产或无价值看涨期权（Asset-or-Nothing Call），在tp时刻，如果S>h，则获得S_tp的期望价值。
    # 其在t=0时刻的价值为 S0 * N(d1)，其中行权价为h，到期日为tp。
    d1_A1 = (np.log(S0 / h) + u_hat * tp) / (sigma * np.sqrt(tp))
    val_A1 = S0 * norm.cdf(d1_A1)

    # ----------------- 3. 路径A2: 保护期后(tp)、到期前(T)首次触及赎回价(h)的价值 -----------------
    # 金融含义：这是一个首次触及障碍期权（First-Touch Barrier Option）的价值。
    # 当股价在(tp, T]区间内首次触及h时，获得价值h。需要通过对首达时间的概率密度函数进行积分计算。
    # 使用数值积分(quad)来计算期望贴现价值。
    def first_hit_pdf_integrand(t):
        # 这是股价在t时刻首次触及障碍h的风险中性概率密度函数，再乘以贴现因子
        if t == 0: return 0
        ln_ratio = np.log(h / S0)
        pdf = (ln_ratio - u * t) / (sigma * np.sqrt(2 * np.pi * t**3)) * \
              np.exp(- (ln_ratio - u * t)**2 / (2 * sigma**2 * t))
        return np.exp(-rf * t) * pdf

    # 积分区间为 (tp, T)
    try:
        val_A2_prob_discounted, _ = quad(first_hit_pdf_integrand, tp, T, epsrel=1e-6, limit=100)
        val_A2 = h * val_A2_prob_discounted
    except:
        val_A2 = 0.0
    
    # ----------------- 4. 路径A3: 到期日(T)才转股的价值 -----------------
    # 金融含义：在到期日T，股价既未在tp触及h，也未在(tp,T]区间触及h，且最终股价S_T高于到期本息fv_N。
    # 这本质上是一个带"向上敲出"障碍（Up-and-Out Barrier）的资产或无价值看涨期权。
    # 其价值可以分解为：[一个无障碍的资产期权价值] - [一个在(0,T]区间内触及障碍h的资产期权价值]
    
    # 定义一个标准的"资产或无价值"看涨期权定价函数
    def asset_or_nothing_call(S, K, t_maturity, r, vol):
        if t_maturity <= 0 or vol <= 0: return S if S > K else 0
        d1 = (np.log(S / K) + (r + 0.5 * vol**2) * t_maturity) / (vol * np.sqrt(t_maturity))
        return S * norm.cdf(d1)

    # 计算一个无障碍、到期日为T、行权价为K1_fv_N的资产期权价值
    val_AON_no_barrier = asset_or_nothing_call(S0, K1_fv_N, T, rf, sigma)

    # 计算一个向上敲出障碍的资产期权价值 (行权价为K1_fv_N，障碍价为h)
    # 标准公式为 S0 * (h/S0)^(2*u_hat/sigma^2) * N(d_barrier)
    if h > 0 and S0 > 0:
        d_barrier = (np.log(h**2 / (S0 * K1_fv_N)) + u_hat * T) / (sigma * np.sqrt(T))
        val_AON_knock_out = S0 * (h / S0)**(2 * u_hat / sigma**2) * norm.cdf(d_barrier)
    else:
        val_AON_knock_out = 0.0
    
    # 路径A3的价值约等于 [无障碍价值] - [敲出部分价值]
    val_A3 = val_AON_no_barrier - val_AON_knock_out
    
    # A1, A2, A3是互斥事件，因此可以直接相加
    # 同时要确保每个路径的价值都非负
    final_equity_value = max(0, val_A1) + max(0, val_A2) + max(0, val_A3)
    
    return final_equity_value

# =======================================================================================
# 动态无风险利率处理
# =======================================================================================

def load_risk_free_rates():
    """
    加载动态无风险利率曲线数据
    返回：包含每个日期插值函数的字典
    """
    rf_file_path = os.path.expanduser('~/Desktop/无风险利率曲线.xlsx')
    
    print(f"加载无风险利率曲线: {rf_file_path}")
    
    try:
        # 读取无风险利率数据
        rf_df = pd.read_excel(rf_file_path, index_col=0)
        rf_df.index = pd.to_datetime(rf_df.index)
        
        # 将期限列名转换为年数
        term_mapping = {
            '1个月': 1/12, '3个月': 0.25, '6个月': 0.5, '9个月': 0.75,
            '1年': 1.0, '2年': 2.0, '3年': 3.0, '4年': 4.0, '5年': 5.0, '6年': 6.0,
            '7年': 7.0, '8年': 8.0, '9年': 9.0, '10年': 10.0
        }
        
        # 重命名列
        rf_df.columns = [term_mapping.get(col, float(col.replace('年', '').replace('个月', '')) if '年' in col or '个月' in col else col) 
                        for col in rf_df.columns]
        
        # 确保列名为数值并排序
        numeric_cols = [col for col in rf_df.columns if isinstance(col, (int, float))]
        rf_df = rf_df[sorted(numeric_cols)]
        
        # 为每个日期创建插值函数
        interpolation_funcs = {}
        
        for date, row in rf_df.iterrows():
            # 去除NaN值
            valid_data = row.dropna()
            if len(valid_data) >= 2:  # 至少需要2个点进行插值
                terms = np.array(valid_data.index)
                rates = np.array(valid_data.values)
                
                # 创建线性插值函数
                interp_func = interp1d(terms, rates, kind='linear', 
                                     bounds_error=False, fill_value='extrapolate')
                interpolation_funcs[date] = interp_func
        
        print(f"成功加载 {len(interpolation_funcs)} 个交易日的利率曲线")
        return interpolation_funcs
        
    except Exception as e:
        print(f"警告：无法加载无风险利率文件 {e}")
        print("将使用固定利率 2.5%")
        return {}

def get_dynamic_risk_free_rate(date, term_years, rf_interpolation_funcs):
    """
    获取动态无风险利率
    
    参数:
    date: 交易日期
    term_years: 期限（年）
    rf_interpolation_funcs: 插值函数字典
    
    返回:
    float: 无风险利率（小数形式）
    """
    if not rf_interpolation_funcs:
        return 0.025  # 默认2.5%
    
    # 寻找最近的交易日
    available_dates = list(rf_interpolation_funcs.keys())
    closest_date = min(available_dates, key=lambda x: abs((x - date).days))
    
    try:
        interp_func = rf_interpolation_funcs[closest_date]
        rate_percent = interp_func(term_years)
        return float(rate_percent) / 100.0  # 转换为小数形式
    except:
        return 0.025  # 默认2.5%

# =======================================================================================
# 任务1：数据清洗、整合与过滤
# =======================================================================================

def load_bond_name_mapping():
    """
    从债券收盘价Sheet读取证券代码到证券简称的映射
    """
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')
    
    print("读取证券代码到证券简称的映射...")
    
    # 读取前两行
    df_header = pd.read_excel(file_path, sheet_name='债券收盘价', nrows=2)
    
    # 第一行是证券代码，第二行是证券简称
    codes = df_header.iloc[0, 1:].values  # 跳过第一列（时间列）
    names = df_header.iloc[1, 1:].values  # 跳过第一列（时间列）
    
    # 创建映射字典
    code_to_name = {}
    for code, name in zip(codes, names):
        if pd.notna(code) and pd.notna(name):
            code_to_name[code] = name
    
    print(f"成功创建 {len(code_to_name)} 个证券代码到简称的映射")
    return code_to_name

def load_and_integrate_data():
    """
    任务1：数据清洗、整合与过滤
    使用pandas.melt高效整合全量数据
    """
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')

    print("开始数据清洗、整合与过滤...")
    print(f"读取文件: {file_path}")

    # 获取证券代码到简称的映射
    code_to_name = load_bond_name_mapping()

    # 定义需要读取的Sheet列表（按照任务要求）
    sheet_list = [
        '债券收盘价', '债券均价', '正股收盘价', '正股均价',
        '剩余期限', '纯债价值', '转股价', '转换价值', '隐含波动率',
        '债券余额', '发行利率', '发行信用评级'
    ]

    # 存储所有长格式数据
    all_long_dfs = []

    # 逐个Sheet处理
    for sheet_name in sheet_list:
        print(f"处理Sheet: {sheet_name}")
        try:
            # 读取整个Sheet
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # 跳过前两行（证券代码和简称），从第三行开始是数据
            df_data = df.iloc[2:].reset_index(drop=True)

            # 设置第一列为时间索引
            df_data.set_index(df_data.columns[0], inplace=True)
            df_data.index = pd.to_datetime(df_data.index)
            df_data.index.name = 'date'

            # 使用pandas.melt转换为长格式
            df_long = df_data.reset_index().melt(
                id_vars=['date'],
                var_name='证券代码',
                value_name=sheet_name
            )
            df_long = df_long.rename(columns={'date': '交易日期'})

            # 只保留非空值
            df_long = df_long.dropna(subset=[sheet_name])

            all_long_dfs.append(df_long)

        except Exception as e:
            print(f"警告：无法读取Sheet '{sheet_name}': {e}")
            continue

    # 合并所有数据
    print("合并所有Sheet数据...")
    result_df = None

    for df in all_long_dfs:
        if result_df is None:
            result_df = df
        else:
            result_df = result_df.merge(df, on=['交易日期', '证券代码'], how='outer')

    # 添加证券简称
    result_df['证券简称'] = result_df['证券代码'].map(code_to_name)

    # 清洗规则：删除所有指标均为空值的记录
    print("应用清洗规则：删除所有指标均为空值的记录...")
    indicator_cols = [col for col in result_df.columns
                     if col not in ['交易日期', '证券代码', '证券简称']]

    # 计算每行非空指标数量
    result_df['non_null_count'] = result_df[indicator_cols].count(axis=1)

    # 删除所有指标均为空的记录
    before_count = len(result_df)
    result_df = result_df[result_df['non_null_count'] > 0].drop('non_null_count', axis=1)
    after_count = len(result_df)

    print(f"清洗前记录数: {before_count:,}")
    print(f"清洗后记录数: {after_count:,}")
    print(f"删除记录数: {before_count - after_count:,}")

    # 初始过滤：债券余额 > 3亿元 且 信用评级在指定范围内
    print("应用初始过滤条件...")

    # 过滤条件1：债券余额 > 3
    if '债券余额' in result_df.columns:
        before_filter1 = len(result_df)
        result_df = result_df[result_df['债券余额'] > 3]
        after_filter1 = len(result_df)
        print(f"债券余额过滤：{before_filter1:,} -> {after_filter1:,} (删除 {before_filter1-after_filter1:,})")

    # 过滤条件2：发行信用评级在 ['AA', 'AA+', 'AAA'] 中
    if '发行信用评级' in result_df.columns:
        before_filter2 = len(result_df)
        valid_ratings = ['AA', 'AA+', 'AAA']
        result_df = result_df[result_df['发行信用评级'].isin(valid_ratings)]
        after_filter2 = len(result_df)
        print(f"信用评级过滤：{before_filter2:,} -> {after_filter2:,} (删除 {before_filter2-after_filter2:,})")

    # 重新排列列顺序（按照任务要求）
    final_cols = [
        '证券代码', '证券简称', '交易日期', '债券收盘价', '债券均价',
        '正股收盘价', '正股均价', '剩余期限', '纯债价值', '转股价',
        '转换价值', '隐含波动率', '债券余额', '发行利率', '发行信用评级'
    ]

    # 只保留存在的列
    available_cols = [col for col in final_cols if col in result_df.columns]
    result_df = result_df[available_cols]

    # 按交易日期和证券代码排序
    result_df = result_df.sort_values(['交易日期', '证券代码']).reset_index(drop=True)

    print(f"最终数据形状: {result_df.shape}")
    print(f"时间范围: {result_df['交易日期'].min()} 到 {result_df['交易日期'].max()}")
    print(f"转债数量: {result_df['证券代码'].nunique()}")

    return result_df

# =======================================================================================
# 任务2：计算CCB理论价值和定价偏离因子
# =======================================================================================

def calculate_ccb_pricing_with_dynamic_rates(df, rf_interpolation_funcs):
    """
    任务2：计算CCB理论价值和定价偏离因子
    使用动态无风险利率和高精度CCB模型
    """
    print("开始计算CCB理论价值和定价偏离因子...")

    df = df.copy()

    # 计算动态剩余保护期
    def calculate_remaining_protection_period(row):
        """
        计算动态剩余保护期
        假设从发行日开始有6个月保护期
        """
        remaining_term = row['剩余期限']
        if pd.isna(remaining_term):
            return np.nan

        # 简化处理：假设剩余保护期 = min(总保护期, 剩余期限)
        return min(TOTAL_PROTECTION_YEARS, remaining_term)

    # 计算CCB股权价值
    def compute_ccb_equity(row):
        try:
            # 提取必要参数
            S0 = row['转换价值']  # 当前转股价值
            sigma = row['隐含波动率']  # 年化波动率
            T = row['剩余期限']  # 剩余到期时间（年）
            conversion_price = row['转股价']  # 转股价
            bond_value = row['纯债价值']  # 纯债价值
            trade_date = row['交易日期']  # 交易日期

            # 检查数据完整性
            if pd.isna(S0) or pd.isna(sigma) or pd.isna(T) or pd.isna(conversion_price) or pd.isna(bond_value):
                return np.nan, np.nan, np.nan  # 返回 (股权价值, 理论价值, 偏离因子)

            if S0 <= 0 or sigma <= 0 or T <= 0 or conversion_price <= 0 or bond_value <= 0:
                return np.nan, np.nan, np.nan

            # 计算CCB模型参数
            h = CALL_COEF * conversion_price  # 赎回触发价 = 130% * 转股价
            K1_fv_N = bond_value  # 经信用风险调整后的到期本息终值

            # 计算剩余保护期
            tp = min(TOTAL_PROTECTION_YEARS, T)  # 保护期不超过剩余期限

            # 获取动态无风险利率
            rf = get_dynamic_risk_free_rate(trade_date, T, rf_interpolation_funcs)

            # 使用高精度CCB模型计算股权价值
            equity_value = ccb_equity_value_precise(
                S0=S0,
                K1_fv_N=K1_fv_N,
                h=h,
                rf=rf,
                sigma=sigma,
                tp=tp,
                T=T
            )

            # 计算CCB理论价值 = CCB股权价值 + 纯债价值
            theoretical_value = equity_value + bond_value

            # 计算定价偏离因子 = (实际价格/CCB理论价格) - 1
            actual_price = row['债券收盘价']
            if pd.notna(actual_price) and theoretical_value > 0:
                pricing_deviation = (actual_price / theoretical_value) - 1
            else:
                pricing_deviation = np.nan

            return equity_value, theoretical_value, pricing_deviation

        except Exception as e:
            return np.nan, np.nan, np.nan

    print("计算CCB股权价值、理论价值和定价偏离因子...")

    # 应用计算函数
    ccb_results = df.apply(compute_ccb_equity, axis=1, result_type='expand')
    ccb_results.columns = ['CCB股权价值', 'CCB理论价值', 'CCB定价偏离因子']

    # 将结果添加到原DataFrame
    df = pd.concat([df, ccb_results], axis=1)

    return df

def calculate_additional_factors(df):
    """
    计算其他重要因子
    """
    print("计算其他重要因子...")

    df = df.copy()

    # 转股溢价率 = (转债价格 - 转换价值) / 转换价值
    df['转股溢价率'] = (df['债券收盘价'] - df['转换价值']) / df['转换价值']

    # 纯债溢价率 = (转债价格 - 纯债价值) / 纯债价值
    df['纯债溢价率'] = (df['债券收盘价'] - df['纯债价值']) / df['纯债价值']

    # 转股价值占比 = 转换价值 / 转债价格
    df['转股价值占比'] = df['转换价值'] / df['债券收盘价']

    # 纯债价值占比 = 纯债价值 / 转债价格
    df['纯债价值占比'] = df['纯债价值'] / df['债券收盘价']

    # CCB股权价值占比 = CCB股权价值 / CCB理论价值
    df['CCB股权价值占比'] = df['CCB股权价值'] / df['CCB理论价值']

    # Delta近似 = 转股价值占比（期权的Delta近似）
    df['Delta近似'] = df['转股价值占比']

    # 转债分类
    def classify_bond(row):
        conversion_premium = row['转股溢价率']
        conversion_ratio = row['转股价值占比']

        if pd.isna(conversion_premium) or pd.isna(conversion_ratio):
            return '未分类'

        # 偏股型：转股溢价率较低，转股价值占比较高
        if conversion_premium < 0.2 and conversion_ratio > 0.7:
            return '偏股型'
        # 偏债型：转股溢价率较高，转股价值占比较低
        elif conversion_premium > 0.5 and conversion_ratio < 0.5:
            return '偏债型'
        # 平衡型：介于两者之间
        else:
            return '平衡型'

    df['转债类型'] = df.apply(classify_bond, axis=1)

    return df

def analyze_ccb_results(df):
    """
    分析CCB模型计算结果
    """
    print("\n" + "="*80)
    print("CCB模型高精度定价分析结果")
    print("="*80)

    print(f"总记录数: {len(df):,}")
    print(f"转债数量: {df['证券代码'].nunique():,}")
    print(f"时间范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")

    # CCB模型有效性检查
    ccb_valid = df['CCB理论价值'].notna()
    print(f"\nCCB模型计算有效性:")
    print(f"  成功计算CCB理论价值的记录: {ccb_valid.sum():,} ({ccb_valid.mean()*100:.1f}%)")

    if ccb_valid.sum() > 0:
        valid_df = df[ccb_valid]

        # 转债类型分布
        print(f"\n转债类型分布:")
        type_dist = valid_df['转债类型'].value_counts()
        for bond_type, count in type_dist.items():
            pct = count / len(valid_df) * 100
            print(f"  {bond_type}: {count:,} ({pct:.1f}%)")

        # 关键指标统计
        print(f"\n关键指标统计（基于{len(valid_df):,}个有效样本）:")

        metrics = {
            'CCB定价偏离因子': valid_df['CCB定价偏离因子'],
            '转股溢价率': valid_df['转股溢价率'],
            '纯债溢价率': valid_df['纯债溢价率'],
            '转股价值占比': valid_df['转股价值占比'],
            'CCB股权价值占比': valid_df['CCB股权价值占比']
        }

        for metric_name, metric_data in metrics.items():
            if metric_data.notna().sum() > 0:
                clean_data = metric_data.dropna()
                print(f"  {metric_name}:")
                print(f"    均值: {clean_data.mean():.4f}")
                print(f"    标准差: {clean_data.std():.4f}")
                print(f"    中位数: {clean_data.median():.4f}")
                print(f"    25%分位数: {clean_data.quantile(0.25):.4f}")
                print(f"    75%分位数: {clean_data.quantile(0.75):.4f}")

        # CCB模型理论价值分析
        print(f"\nCCB模型理论价值分析:")
        ccb_theory = valid_df['CCB理论价值']
        bond_price = valid_df['债券收盘价']
        ccb_equity = valid_df['CCB股权价值']
        bond_value = valid_df['纯债价值']

        print(f"  CCB理论价值均值: {ccb_theory.mean():.2f}")
        print(f"  实际债券价格均值: {bond_price.mean():.2f}")
        print(f"  CCB股权价值均值: {ccb_equity.mean():.2f}")
        print(f"  纯债价值均值: {bond_value.mean():.2f}")
        print(f"  理论价值/实际价格比值: {(ccb_theory.mean()/bond_price.mean()):.4f}")

        # 定价偏离分析
        deviation = valid_df['CCB定价偏离因子'].dropna()
        if len(deviation) > 0:
            print(f"\n定价偏离深度分析:")
            print(f"  负偏离（低估）比例: {(deviation < 0).mean()*100:.1f}%")
            print(f"  正偏离（高估）比例: {(deviation > 0).mean()*100:.1f}%")
            print(f"  偏离绝对值均值: {deviation.abs().mean():.4f}")
            print(f"  偏离绝对值中位数: {deviation.abs().median():.4f}")

        # 按转债类型分析
        print(f"\n按转债类型的定价偏离分析:")
        for bond_type in valid_df['转债类型'].unique():
            if pd.notna(bond_type):
                type_data = valid_df[valid_df['转债类型'] == bond_type]['CCB定价偏离因子'].dropna()
                if len(type_data) > 0:
                    print(f"  {bond_type}: 均值={type_data.mean():.4f}, 中位数={type_data.median():.4f}, 样本数={len(type_data):,}")

def main():
    """
    主函数：执行完整的CCB模型定价流程
    """
    print("可转债CCB模型高精度定价与全流程因子计算")
    print("="*60)

    # 加载动态无风险利率
    rf_interpolation_funcs = load_risk_free_rates()

    # 任务1：数据清洗、整合与过滤
    df = load_and_integrate_data()

    # 任务2：计算CCB理论价值和定价偏离因子
    df = calculate_ccb_pricing_with_dynamic_rates(df, rf_interpolation_funcs)

    # 计算其他因子
    df = calculate_additional_factors(df)

    # 分析结果
    analyze_ccb_results(df)

    # 保存结果
    output_dir = os.path.expanduser('~/Desktop/转债量化')
    os.makedirs(output_dir, exist_ok=True)

    output_file = os.path.join(output_dir, 'CCB模型高精度定价全流程结果.xlsx')

    print(f"\n保存结果到: {output_file}")
    df.to_excel(output_file, index=False)

    # 显示样本数据
    print(f"\n样本数据预览:")
    display_cols = [
        '证券代码', '证券简称', '交易日期', '债券收盘价', 'CCB理论价值',
        'CCB定价偏离因子', '转股溢价率', '转债类型'
    ]
    available_display_cols = [col for col in display_cols if col in df.columns]
    print(df[available_display_cols].head(10).to_string(index=False))

    print(f"\n处理完成！")
    print(f"最终数据形状: {df.shape}")
    print(f"输出文件: {output_file}")

    return df

if __name__ == "__main__":
    result_df = main()
