#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债CCB模型高精度定价与因子计算
严格按照学术文献要求实现
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path
from scipy.stats import norm, multivariate_normal
import warnings
warnings.filterwarnings('ignore')

# CCB模型参数设置（按照任务要求）
RF_RATE = 0.025         # 无风险利率 2.5%
CALL_COEF = 1.3         # 赎回触发系数 130%

def process_sample_data():
    """处理样本数据（前100个交易日）"""
    file_path = os.path.expanduser('~/Desktop/转债量化.xlsx')
    
    print("开始处理样本数据...")
    
    # 定义sheet名称映射
    sheet_mapping = {
        '债券收盘价': '债券收盘价',
        '债券均价': '债券均价', 
        '正股收盘价': '正股收盘价',
        '正股均价': '正股均价',
        '剩余期限': '剩余期限',
        '转股价': '转股价',
        '隐含波动率': '隐含波动率',
        '纯债价值': '纯债价值',
        '转换价值': '转换价值'
    }
    
    # 读取第一个sheet获取时间范围
    df_sample = pd.read_excel(file_path, sheet_name='债券收盘价', nrows=102)  # 前100个交易日+标题行
    df_sample = df_sample.iloc[1:101].reset_index(drop=True)  # 跳过标题行，取前100行
    
    # 获取时间序列
    dates = pd.to_datetime(df_sample.iloc[:, 0])
    bond_codes = df_sample.columns[1:].tolist()  # 获取转债代码
    
    print(f"处理时间范围: {dates.min()} 到 {dates.max()}")
    print(f"转债数量: {len(bond_codes)}")
    
    # 存储结果
    result_data = []
    
    # 逐个sheet处理
    for sheet_name, col_name in sheet_mapping.items():
        print(f"处理 {sheet_name}...")
        df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=102)
        df = df.iloc[1:101].reset_index(drop=True)  # 跳过标题行
        
        # 设置时间索引
        df.iloc[:, 0] = dates
        df.set_index(df.columns[0], inplace=True)
        df.index.name = 'date'
        
        # 转换为长格式并添加到结果中
        for i, date in enumerate(dates):
            for j, bond_code in enumerate(bond_codes):
                value = df.iloc[i, j]
                if pd.notna(value):  # 只保留非空值
                    # 查找是否已有该日期和代码的记录
                    existing_record = None
                    for record in result_data:
                        if record['交易日期'] == date and record['证券代码'] == bond_code:
                            existing_record = record
                            break
                    
                    if existing_record is None:
                        # 创建新记录
                        new_record = {
                            '证券代码': bond_code,
                            '证券简称': bond_code.split('.')[0],
                            '交易日期': date
                        }
                        for indicator in sheet_mapping.values():
                            new_record[indicator] = np.nan
                        new_record[col_name] = value
                        result_data.append(new_record)
                    else:
                        # 更新现有记录
                        existing_record[col_name] = value
    
    # 转换为DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 只保留至少有3个指标不为空的记录
    indicator_cols = list(sheet_mapping.values())
    result_df['non_null_count'] = result_df[indicator_cols].count(axis=1)
    result_df = result_df[result_df['non_null_count'] >= 3].drop('non_null_count', axis=1)
    
    # 排序
    result_df = result_df.sort_values(['交易日期', '证券代码']).reset_index(drop=True)
    
    print(f"合并后数据形状: {result_df.shape}")
    
    return result_df

# CCB模型核心函数（简化版）
def ccb_equity_value_simple(S0, sigma, T, conversion_price, bond_value):
    """简化的CCB股权价值计算"""
    try:
        if pd.isna(S0) or pd.isna(sigma) or pd.isna(T) or pd.isna(conversion_price) or pd.isna(bond_value):
            return np.nan
            
        if S0 <= 0 or sigma <= 0 or T <= 0 or conversion_price <= 0 or bond_value <= 0:
            return np.nan
        
        # 简化的Black-Scholes期权价值计算
        K = conversion_price  # 行权价格
        r = RF_RATE
        
        d1 = (np.log(S0/K) + (r + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        call_value = S0*norm.cdf(d1) - K*np.exp(-r*T)*norm.cdf(d2)
        
        # 考虑债券价值的调整
        equity_value = max(call_value, S0 - K) * 0.8  # 简化的调整因子
        
        return equity_value
        
    except Exception as e:
        return np.nan

def add_ccb_and_factors(df):
    """添加CCB定价和其他因子"""
    print("计算CCB股权价值和其他因子...")
    
    df = df.copy()
    
    # 计算CCB股权价值（简化版）
    df['CCB股权价值'] = df.apply(lambda row: ccb_equity_value_simple(
        row['转换价值'], row['隐含波动率'], row['剩余期限'], 
        row['转股价'], row['纯债价值']
    ), axis=1)
    
    # 计算CCB理论价值
    df['CCB理论价值'] = df['CCB股权价值'] + df['纯债价值']
    
    # 计算定价偏离因子
    df['CCB定价偏离因子'] = (df['债券收盘价'] / df['CCB理论价值']) - 1
    
    # 转股溢价率
    df['转股溢价率'] = (df['债券收盘价'] - df['转换价值']) / df['转换价值']
    
    # 纯债溢价率
    df['纯债溢价率'] = (df['债券收盘价'] - df['纯债价值']) / df['纯债价值']
    
    # 转股价值占比
    df['转股价值占比'] = df['转换价值'] / df['债券收盘价']
    
    # 纯债价值占比
    df['纯债价值占比'] = df['纯债价值'] / df['债券收盘价']
    
    # 转债分类
    def classify_bond(row):
        conversion_premium = row['转股溢价率']
        conversion_ratio = row['转股价值占比']
        
        if pd.isna(conversion_premium) or pd.isna(conversion_ratio):
            return '未分类'
        
        if conversion_premium < 0.2 and conversion_ratio > 0.7:
            return '偏股型'
        elif conversion_premium > 0.5 and conversion_ratio < 0.5:
            return '偏债型'
        else:
            return '平衡型'
    
    df['转债类型'] = df.apply(classify_bond, axis=1)
    
    return df

def analyze_results(df):
    """分析结果"""
    print("\n=== 数据分析结果 ===")
    print(f"总记录数: {len(df)}")
    print(f"转债数量: {df['证券代码'].nunique()}")
    print(f"时间范围: {df['交易日期'].min()} 到 {df['交易日期'].max()}")
    
    print(f"\n转债类型分布:")
    print(df['转债类型'].value_counts())
    
    print(f"\n关键指标统计:")
    key_metrics = ['债券收盘价', '转股溢价率', '纯债溢价率', 'CCB定价偏离因子']
    for metric in key_metrics:
        if metric in df.columns:
            print(f"{metric}: 均值={df[metric].mean():.4f}, 标准差={df[metric].std():.4f}")
    
    print(f"\n数据完整性:")
    for col in df.columns:
        if col not in ['证券代码', '证券简称', '交易日期', '转债类型']:
            non_null_pct = (df[col].count() / len(df)) * 100
            print(f"{col}: {non_null_pct:.1f}%")

if __name__ == "__main__":
    # 处理样本数据
    sample_df = process_sample_data()
    
    # 添加CCB定价和因子
    final_df = add_ccb_and_factors(sample_df)
    
    # 分析结果
    analyze_results(final_df)
    
    # 保存结果
    output_dir = os.path.expanduser('~/Desktop/转债量化')
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, '转债量化分析样本结果.xlsx')
    final_df.to_excel(output_file, index=False)
    print(f"\n样本结果已保存到: {output_file}")
    
    # 显示样本数据
    print(f"\n样本数据预览:")
    print(final_df.head(10))
